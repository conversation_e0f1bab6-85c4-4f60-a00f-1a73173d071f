/**
 * @file adc_display.c
 * @brief ADC Data Display Implementation
 * <AUTHOR> for CH32V003 Project
 * @date 2025
 */

#include "adc_display.h"
#include "touch_button.h"
#include "debug.h"
#include "battery_levels.h"
#include <stdio.h>
#include <string.h>


/**
 * @brief Format voltage value for display
 * @param voltage_mv Voltage in millivolts
 * @param buffer Output buffer
 * @param buffer_size Size of output buffer
 */
void ADC_Display_Format_Voltage(uint16_t voltage_mv, char* buffer, uint8_t buffer_size)
{
    if (buffer == NULL) return;
    
    uint16_t volts = voltage_mv / 1000;
    uint16_t millivolts = voltage_mv % 1000;
    
    snprintf(buffer, buffer_size, "%d.%02dV", volts, millivolts / 10);
}

/**
 * @brief Draw logo-style display similar to the reference image
 * @param adc_data Pointer to ADC data structure
 */
void ADC_Display_Draw_Logo_Style(ADC_Data_t* adc_data)
{
    // Clear screen first
    tft_fill_rect(0, 0, ST7735_WIDTH, ST7735_HEIGHT, BLACK);

    // Draw the main display area (black rectangle with border)
    ADC_Display_Draw_Logo_Border();

    // Draw header/logo area
    // ADC_Display_Draw_Logo_Header();

    // Draw channel indicators
    ADC_Display_Draw_Logo_Channels(adc_data);

    // Draw main voltage display (total of all channels)
    ADC_Display_Draw_Logo_Main_Voltage(adc_data);
}

/**
 * @brief Draw the border and main display area
 */
void ADC_Display_Draw_Logo_Border(void)
{
    // Draw outer green border (3 pixel border)
    tft_fill_rect(0, 0, ST7735_WIDTH, ST7735_HEIGHT, GREEN);

    // Draw inner black display area
    tft_fill_rect(3, 3, ST7735_WIDTH - 6, ST7735_HEIGHT - 6, LOGO_BACKGROUND_COLOR);

    // Top line
    tft_fill_rect(5, 5, ST7735_WIDTH - 10, 1, LOGO_TEXT_COLOR);
    // Bottom line
    tft_fill_rect(5, ST7735_HEIGHT - 6, ST7735_WIDTH - 10, 1, LOGO_TEXT_COLOR);
    // Left line
    tft_fill_rect(5, 5, 1, ST7735_HEIGHT - 10, LOGO_TEXT_COLOR);
    // Right line
    tft_fill_rect(ST7735_WIDTH - 6, 5, 1, ST7735_HEIGHT - 10, LOGO_TEXT_COLOR);
}

/**
 * @brief Draw header/logo area with Vietnam flag
 */
void ADC_Display_Draw_Logo_Header(void)
{
    tft_set_color(LOGO_TEXT_COLOR);
    tft_set_background_color(LOGO_BACKGROUND_COLOR);

    tft_set_cursor(35, 10);
    tft_print("CH32V");

    tft_set_cursor(35, 20);
    tft_print("VIETNAM");

    // Draw some decorative lines
    tft_fill_rect(90, 12, 30, 1, LOGO_TEXT_COLOR);
    tft_fill_rect(90, 15, 25, 1, LOGO_TEXT_COLOR);
    tft_fill_rect(90, 18, 20, 1, LOGO_TEXT_COLOR);
}

/**
 * @brief Draw channel indicators on the left side
 * @param adc_data Pointer to ADC data structure
 */
void ADC_Display_Draw_Logo_Channels(ADC_Data_t* adc_data)
{
    if (adc_data == NULL) return;

    tft_set_color(LOGO_CHANNEL_COLOR);
    tft_set_background_color(LOGO_BACKGROUND_COLOR);

    char buffer[8];

    // Draw 4 channel indicators vertically on the left
    for (uint8_t i = 0; i < 4; i++) {
        uint16_t y_pos = 35 + (i * 10);

        // Draw channel number
        tft_set_cursor(10, y_pos);
        snprintf(buffer, sizeof(buffer), "CH%d", i + 1);
        tft_print(buffer);

        // Draw voltage value next to channel
        tft_set_cursor(35, y_pos);
        if (adc_data->channel_ready[i]) {
            ADC_Display_Format_Voltage(adc_data->voltage_mv[i], buffer, sizeof(buffer));
            tft_print(buffer);
        } else {
            tft_print("-.--V");
        }
    }
}

/**
 * @brief Draw the main voltage display (large text) - shows total of all channels
 * @param adc_data Pointer to ADC data structure
 */
void ADC_Display_Draw_Logo_Main_Voltage(ADC_Data_t* adc_data)
{
    if (adc_data == NULL) return;

    tft_set_color(LOGO_VOLTAGE_COLOR);
    tft_set_background_color(LOGO_BACKGROUND_COLOR);

    // Calculate total voltage from all 4 channels
    uint32_t total_voltage_mv = 0;
    uint8_t active_channels = 0;

    for (uint8_t i = 0; i < 4; i++) {
        if (adc_data->channel_ready[i]) {
            total_voltage_mv += adc_data->voltage_mv[i];
            active_channels++;
        }
    }

    // Display total voltage in large text
    char buffer[16];

    if (active_channels > 0) {
        // Format total voltage with 3 decimal places
        uint16_t volts = total_voltage_mv / 1000;
        uint16_t millivolts = total_voltage_mv % 1000;
        snprintf(buffer, sizeof(buffer), "%d.%03dV", volts, millivolts);
    } else {
        snprintf(buffer, sizeof(buffer), "-.---V");
    }

    tft_set_cursor(95, 35);
    tft_print(buffer);

    // Draw battery icon at the bottom with level based on total voltage
    if (active_channels > 0) {
        Draw_Battery_Level(95, 45, total_voltage_mv, LOGO_BACKGROUND_COLOR);
    } else {
        // Draw empty battery if no channels active
        Draw_Battery_Level(95, 45, 0, LOGO_BACKGROUND_COLOR);
    }

    tft_set_color(LOGO_VOLTAGE_COLOR);
    tft_set_background_color(LOGO_BACKGROUND_COLOR);
    // draw percent below battery
    if (active_channels > 0) {
        uint16_t percentage = total_voltage_mv * 100 / 16800;
        snprintf(buffer, sizeof(buffer), "%d%%", percentage);
    } else {
        snprintf(buffer, sizeof(buffer), "--%%");
    }
    tft_set_cursor(105, 65);
    tft_print(buffer);

}

/**
 * @brief Update only the channel voltage values (not labels)
 * @param adc_data Pointer to ADC data structure
 */
void ADC_Display_Update_Logo_Channel_Values(ADC_Data_t* adc_data)
{
    if (adc_data == NULL) return;

    tft_set_color(LOGO_TEXT_COLOR);
    tft_set_background_color(LOGO_BACKGROUND_COLOR);

    char buffer[8];

    // Update only the voltage values for each channel
    for (uint8_t i = 0; i < 4; i++) {
        uint16_t y_pos = 35 + (i * 10);
        uint16_t value_x = 35;  // X position where voltage values are displayed

        // Clear the voltage value area (approximate width: 30 pixels)
        tft_fill_rect(value_x, y_pos, 30, 8, LOGO_BACKGROUND_COLOR);

        // Draw updated voltage value
        tft_set_cursor(value_x, y_pos);
        if (adc_data->channel_ready[i]) {
            ADC_Display_Format_Voltage(adc_data->voltage_mv[i], buffer, sizeof(buffer));
            tft_print(buffer);
        } else {
            tft_print("-.--V");
        }
    }
}

/**
 * @brief Update only ADC values without redrawing static elements
 * @param adc_data Pointer to ADC data structure
 */
void ADC_Display_Update_Logo_Values_Only(ADC_Data_t* adc_data)
{
    if (adc_data == NULL) return;

    // Update channel values only
    ADC_Display_Update_Logo_Channel_Values(adc_data);

    ADC_Display_Draw_Logo_Main_Voltage(adc_data);
}


